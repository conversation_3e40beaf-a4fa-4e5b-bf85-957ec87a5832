from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException, Form, Depends
from fastapi.responses import FileResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os
import json
import shutil
import httpx
from pathlib import Path
import re

from contextlib import asynccontextmanager

@asynccontextmanager
async def lifespan(app):
    # Create frontend/dist directory if it doesn't exist
    os.makedirs("frontend/dist", exist_ok=True)
    yield

app = FastAPI(title="Language Assistant API", lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Models
class ConfigModel(BaseModel):
    video_directory: str

class SubtitleEntry(BaseModel):
    start: str
    end: str
    text: str
    translation: Optional[str] = None

class OllamaRequest(BaseModel):
    prompt: str
    text: str

# Config file path
CONFIG_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), "config.json")

# Helper functions
def load_config():
    if os.path.exists(CONFIG_FILE):
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    return {"video_directory": ""}

def save_config(config_data):
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)

def parse_time(time_str):
    """Convert SRT/VTT time format to seconds"""
    hours, minutes, seconds = time_str.replace(',', '.').split(':')
    return float(hours) * 3600 + float(minutes) * 60 + float(seconds)

def format_time(seconds):
    """Convert seconds to SRT time format"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    return f"{hours:02d}:{minutes:02d}:{seconds:06.3f}".replace('.', ',')

def parse_srt(file_path):
    """Parse SRT subtitle file"""
    with open(file_path, 'r', encoding='utf-8-sig') as f:
        content = f.read()
    
    # Split by double newline to get subtitle blocks
    subtitle_blocks = re.split(r'\n\n+', content.strip())
    subtitles = []
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) < 3:
            continue
            
        # Skip the subtitle number (first line)
        time_line = lines[1]
        text_lines = lines[2:]
        
        # Parse time range
        time_range = time_line.split(' --> ')
        if len(time_range) != 2:
            continue
            
        start_time, end_time = time_range
        text = '\n'.join(text_lines)
        
        subtitles.append({
            "start": start_time,
            "end": end_time,
            "text": text,
            "translation": None
        })
    
    return subtitles

def parse_vtt(file_path):
    """Parse VTT subtitle file"""
    with open(file_path, 'r', encoding='utf-8-sig') as f:
        content = f.read()
    
    # Skip the WEBVTT header
    if content.startswith('WEBVTT'):
        content = '\n'.join(content.split('\n')[1:])
    
    # Split by double newline to get subtitle blocks
    subtitle_blocks = re.split(r'\n\n+', content.strip())
    subtitles = []
    
    for block in subtitle_blocks:
        lines = block.strip().split('\n')
        if len(lines) < 2:
            continue
            
        # Find the line with the time range
        time_line = None
        for i, line in enumerate(lines):
            if ' --> ' in line:
                time_line = line
                text_lines = lines[i+1:]
                break
        
        if not time_line:
            continue
            
        # Parse time range
        time_range = time_line.split(' --> ')
        if len(time_range) != 2:
            continue
            
        start_time, end_time = time_range
        # Remove cue settings if present
        start_time = start_time.split(' ')[0]
        end_time = end_time.split(' ')[0]
        
        text = '\n'.join(text_lines)
        
        subtitles.append({
            "start": start_time,
            "end": end_time,
            "text": text,
            "translation": None
        })
    
    return subtitles

# Routes
@app.get("/api/config")
async def get_config():
    return load_config()

@app.post("/api/config")
async def update_config(config: ConfigModel):
    config_data = load_config()
    config_data["video_directory"] = config.video_directory
    save_config(config_data)
    return {"status": "success"}

def build_directory_tree(video_dir):
    """Build a nested directory tree structure with videos"""
    def scan_directory(current_path, relative_path=""):
        items = []
        full_path = os.path.join(video_dir, relative_path) if relative_path else video_dir

        if not os.path.exists(full_path):
            return items

        try:
            entries = sorted(os.listdir(full_path))
        except PermissionError:
            return items

        # First add directories
        for entry in entries:
            entry_path = os.path.join(full_path, entry)
            if os.path.isdir(entry_path):
                rel_entry_path = os.path.join(relative_path, entry) if relative_path else entry
                children = scan_directory(entry_path, rel_entry_path)
                items.append({
                    "type": "directory",
                    "name": entry,
                    "path": rel_entry_path,
                    "children": children
                })

        # Then add video files
        for entry in entries:
            entry_path = os.path.join(full_path, entry)
            if os.path.isfile(entry_path) and entry.lower().endswith((".mp4", ".webm", ".mkv", ".avi")):
                rel_entry_path = os.path.join(relative_path, entry) if relative_path else entry

                # Check for matching subtitle files
                base_name = os.path.splitext(entry_path)[0]
                srt_path = f"{base_name}.srt"
                vtt_path = f"{base_name}.vtt"

                has_srt = os.path.exists(srt_path)
                has_vtt = os.path.exists(vtt_path)

                items.append({
                    "type": "video",
                    "name": entry,
                    "path": rel_entry_path.replace("\\", "/"),  # Normalize path separators
                    "has_srt": has_srt,
                    "has_vtt": has_vtt
                })

        return items

    return scan_directory(video_dir)

@app.get("/api/videos")
async def list_videos():
    config = load_config()
    video_dir = config.get("video_directory", "")

    if not video_dir or not os.path.exists(video_dir):
        return {"tree": []}

    tree = build_directory_tree(video_dir)
    return {"tree": tree}

@app.get("/api/video/{path:path}")
async def get_video(path: str):
    config = load_config()
    video_dir = config.get("video_directory", "")
    
    if not video_dir:
        raise HTTPException(status_code=400, detail="Video directory not configured")
    
    file_path = os.path.join(video_dir, path)
    
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Determine the correct media type based on file extension
    media_type = None
    if file_path.lower().endswith('.mp4'):
        media_type = 'video/mp4'
    elif file_path.lower().endswith('.webm'):
        media_type = 'video/webm'
    elif file_path.lower().endswith('.mkv'):
        media_type = 'video/x-matroska'
    elif file_path.lower().endswith('.avi'):
        media_type = 'video/x-msvideo'
    
    # Explicitly set the media_type to ensure proper audio playback
    return FileResponse(file_path, media_type=media_type)

@app.get("/api/subtitles/{path:path}")
async def get_subtitles(path: str, type: Optional[str] = None):
    config = load_config()
    video_dir = config.get("video_directory", "")
    
    if not video_dir:
        raise HTTPException(status_code=400, detail="Video directory not configured")
    
    video_path = os.path.join(video_dir, path)
    base_name = os.path.splitext(video_path)[0]
    
    # If type is specified, use that subtitle file
    if type == "srt":
        subtitle_path = f"{base_name}.srt"
    elif type == "vtt":
        subtitle_path = f"{base_name}.vtt"
    else:
        # Try to find a matching subtitle file
        srt_path = f"{base_name}.srt"
        vtt_path = f"{base_name}.vtt"
        
        if os.path.exists(srt_path):
            subtitle_path = srt_path
        elif os.path.exists(vtt_path):
            subtitle_path = vtt_path
        else:
            raise HTTPException(status_code=404, detail="No subtitle file found")
    
    if not os.path.exists(subtitle_path):
        raise HTTPException(status_code=404, detail="Subtitle file not found")
    
    # Parse the subtitle file
    if subtitle_path.endswith(".srt"):
        subtitles = parse_srt(subtitle_path)
    elif subtitle_path.endswith(".vtt"):
        subtitles = parse_vtt(subtitle_path)
    else:
        raise HTTPException(status_code=400, detail="Unsupported subtitle format")
    
    return {"subtitles": subtitles}

@app.get("/api/subtitles-in-directory/{path:path}")
async def list_subtitles_in_directory(path: str):
    """Get all subtitle files in the same directory as the video"""
    config = load_config()
    video_dir = config.get("video_directory", "")

    if not video_dir:
        raise HTTPException(status_code=400, detail="Video directory not configured")

    # Get the directory containing the video
    video_path = os.path.join(video_dir, path)
    directory_path = os.path.dirname(video_path)

    if not os.path.exists(directory_path):
        raise HTTPException(status_code=404, detail="Directory not found")

    subtitle_files = []

    try:
        for file in os.listdir(directory_path):
            if file.lower().endswith(('.srt', '.vtt')):
                file_path = os.path.join(directory_path, file)
                rel_path = os.path.relpath(file_path, video_dir).replace("\\", "/")

                # Get file extension to determine type
                file_ext = os.path.splitext(file)[1].lower()
                subtitle_type = file_ext[1:]  # Remove the dot

                subtitle_files.append({
                    "name": file,
                    "path": rel_path,
                    "type": subtitle_type
                })
    except PermissionError:
        raise HTTPException(status_code=403, detail="Permission denied accessing directory")

    return {"subtitles": subtitle_files}

@app.post("/api/analyze-pronunciation")
async def analyze_pronunciation(request: OllamaRequest):
    try:
        # Call Ollama API
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:11434/api/generate",
                json={
                    "model": "llama3",  # Make sure this model is available in your Ollama installation
                    "prompt": f"{request.prompt}\n\nText: {request.text}",
                    "stream": False
                }
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=500, detail="Failed to call Ollama API")
            
            result = response.json()
            return {"analysis": result.get("response", "")}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error: {str(e)}")

# Mount static files for frontend (only if directory exists and not in development mode)
# Check if we're running in development mode (when start.py is used)
is_dev_mode = os.environ.get('DEV_MODE') == '1'

frontend_dist_path = "frontend/dist"
if not is_dev_mode and os.path.exists(frontend_dist_path):
    app.mount("/", StaticFiles(directory=frontend_dist_path, html=True), name="static")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)