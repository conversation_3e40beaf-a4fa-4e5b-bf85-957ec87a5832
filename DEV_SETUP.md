# 开发环境设置

## 开发模式 vs 生产模式

### 开发模式 (推荐用于开发)
```bash
python start.py
```

**功能：**
- 同时启动前端开发服务器 (Vite) 和后端服务器
- 前端支持热更新 (Hot Module Replacement)
- 前端运行在 `http://localhost:5173`
- 后端 API 运行在 `http://localhost:8000`
- 前端会自动代理 API 请求到后端

**优势：**
- ✅ 前端代码修改后自动刷新
- ✅ 快速开发体验
- ✅ 支持 React 热更新
- ✅ 自动处理 CORS

### 生产模式
```bash
python backend/main.py
```

**功能：**
- 构建前端静态文件到 `frontend/dist`
- 后端服务器提供静态文件和 API
- 单一服务器运行在 `http://localhost:8000`

## 端口说明

- **前端开发服务器**: `http://localhost:5173` (仅开发模式)
- **后端 API 服务器**: `http://localhost:8000`
- **API 文档**: `http://localhost:8000/docs`

## 开发工作流

1. 启动开发环境：
   ```bash
   python start.py
   ```

2. 访问前端应用：
   ```
   http://localhost:5173
   ```

3. 修改前端代码，浏览器会自动刷新

4. 修改后端代码，服务器会自动重启 (uvicorn reload)

5. 停止服务器：按 `Ctrl+C`

## 故障排除

### 端口被占用
如果端口 5173 或 8000 被占用，请先关闭占用端口的程序。

### 前端无法连接后端
确保后端服务器正在运行，并且 `frontend/vite.config.js` 中的代理配置正确。

### 热更新不工作
1. 确保使用 `python start.py` 启动
2. 检查前端开发服务器是否正常运行
3. 尝试手动刷新浏览器
