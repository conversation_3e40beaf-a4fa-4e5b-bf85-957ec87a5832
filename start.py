import os
import subprocess
import sys
import threading
import time
import signal

def main():
    # Get the project root directory
    project_dir = os.path.dirname(os.path.abspath(__file__))
    frontend_dir = os.path.join(project_dir, 'frontend')
    backend_dir = os.path.join(project_dir, 'backend')

    # Store process references for cleanup
    processes = []

    def signal_handler(signum, frame):
        print("\nShutting down development servers...")
        for process in processes:
            if process.poll() is None:  # Process is still running
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        sys.exit(0)

    # Register signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    def start_frontend():
        """Start the frontend development server"""
        print("Starting frontend development server...")

        # On Windows, we need to use shell=True for npm/pnpm commands
        shell_needed = os.name == 'nt'

        try:
            # Try pnpm first
            try:
                process = subprocess.Popen(['pnpm', 'run', 'dev'], cwd=frontend_dir, shell=shell_needed)
                processes.append(process)
                print("Frontend development server started with pnpm on http://localhost:5173")
                return process
            except (FileNotFoundError, subprocess.SubprocessError):
                # If pnpm is not found, try npm
                print("pnpm not found, trying npm instead...")
                try:
                    process = subprocess.Popen(['npm', 'run', 'dev'], cwd=frontend_dir, shell=shell_needed)
                    processes.append(process)
                    print("Frontend development server started with npm on http://localhost:5173")
                    return process
                except (FileNotFoundError, subprocess.SubprocessError):
                    print("npm not found either. Make sure Node.js and npm are installed and in your PATH.")
                    return None
        except Exception as e:
            print(f"Error starting frontend server: {e}")
            return None

    def start_backend():
        """Start the backend server"""
        print("Starting backend server...")
        try:
            # Set environment variable to indicate development mode
            env = os.environ.copy()
            env['DEV_MODE'] = '1'
            process = subprocess.Popen([sys.executable, 'main.py'], cwd=backend_dir, env=env)
            processes.append(process)
            print("Backend server started on http://localhost:8000")
            return process
        except Exception as e:
            print(f"Error starting backend server: {e}")
            return None

    # Start both servers
    frontend_process = start_frontend()
    if not frontend_process:
        return 1

    # Give frontend a moment to start
    time.sleep(2)

    backend_process = start_backend()
    if not backend_process:
        # Clean up frontend process
        if frontend_process.poll() is None:
            frontend_process.terminate()
        return 1

    print("\n" + "="*60)
    print("🚀 Development environment is ready!")
    print("📱 Frontend (with hot reload): http://localhost:5173")
    print("🔧 Backend API: http://localhost:8000")
    print("📚 API Documentation: http://localhost:8000/docs")
    print("="*60)
    print("Press Ctrl+C to stop both servers")
    print("="*60 + "\n")

    try:
        # Wait for both processes
        while True:
            # Check if any process has died
            if frontend_process.poll() is not None:
                print("Frontend server has stopped")
                break
            if backend_process.poll() is not None:
                print("Backend server has stopped")
                break
            time.sleep(1)
    except KeyboardInterrupt:
        pass
    finally:
        signal_handler(None, None)

    return 0

if __name__ == "__main__":
    sys.exit(main())